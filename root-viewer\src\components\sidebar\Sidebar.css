.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  color: white;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  width: 280px;
}

.sidebar__header {
  padding: 20px 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 70px;
}

.sidebar__logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar__logo-icon {
  font-size: 24px;
  min-width: 24px;
}

.sidebar__logo-text {
  font-size: 18px;
  font-weight: 600;
  white-space: nowrap;
  padding-left: 16px;
}



.sidebar__nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.sidebar__menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar__menu-item {
  margin-bottom: 4px;
}

.sidebar button.sidebar__menu-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: none !important;
  background-color: transparent !important;
  border: none !important;
  color: rgba(255, 255, 255, 0.8) !important;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  width: 100%;
  text-align: left;
  border-radius: 0 !important;
  position: relative;
  font-size: 14px !important;
  font-weight: 500 !important;
  font-family: inherit !important;
}

.sidebar button.sidebar__menu-link:hover:not(.sidebar__menu-link--active) {
  background: rgba(255, 255, 255, 0.1) !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
  border-color: transparent !important;
}

.sidebar button.sidebar__menu-link--active {
  background: rgba(52, 152, 219, 0.2) !important;
  background-color: rgba(52, 152, 219, 0.2) !important;
  color: #3498db !important;
  border-right: 3px solid #3498db !important;
  border-color: transparent !important;
}

.sidebar button.sidebar__menu-link--active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #3498db;
}

.sidebar__menu-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  padding-left: 8px;
  flex: 1;
}

/* 父级菜单项样式 */
.sidebar__menu-link--parent {
  justify-content: space-between;
}

.sidebar__menu-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
  margin-right: 8px;
}

.sidebar__menu-arrow--collapsed {
  transform: rotate(-90deg);
}

/* 多层级菜单项样式 */
.sidebar__menu-item--level-0 {
  margin-bottom: 4px;
}

.sidebar__menu-item--level-1 {
  margin-bottom: 2px;
}

.sidebar__menu-item--level-2 {
  margin-bottom: 1px;
}

.sidebar__menu-item--level-3 {
  margin-bottom: 1px;
}

/* 不同层级的链接样式 */
.sidebar .sidebar__menu-link--level-0 {
  padding: 12px 20px !important;
}

.sidebar .sidebar__menu-link--level-1 {
  padding: 8px 20px 8px 40px !important;
  font-size: 13px !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.sidebar .sidebar__menu-link--level-2 {
  padding: 6px 20px 6px 60px !important;
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

.sidebar .sidebar__menu-link--level-3 {
  padding: 4px 20px 4px 80px !important;
  font-size: 11px !important;
  color: rgba(255, 255, 255, 0.6) !important;
}

/* 不同层级的高亮样式 */
.sidebar button.sidebar__menu-link--level-1.sidebar__menu-link--active {
  background: rgba(52, 152, 219, 0.25) !important;
  background-color: rgba(52, 152, 219, 0.25) !important;
  color: #3498db !important;
  border-right: 3px solid #3498db !important;
  border-color: transparent !important;
}

.sidebar button.sidebar__menu-link--level-1.sidebar__menu-link--active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #3498db;
}

.sidebar button.sidebar__menu-link--level-2.sidebar__menu-link--active {
  background: rgba(52, 152, 219, 0.3) !important;
  background-color: rgba(52, 152, 219, 0.3) !important;
  color: #3498db !important;
  border-right: 3px solid #3498db !important;
  border-color: transparent !important;
}

.sidebar button.sidebar__menu-link--level-2.sidebar__menu-link--active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #3498db;
}

.sidebar button.sidebar__menu-link--level-3.sidebar__menu-link--active {
  background: rgba(52, 152, 219, 0.35) !important;
  background-color: rgba(52, 152, 219, 0.35) !important;
  color: #3498db !important;
  border-right: 3px solid #3498db !important;
  border-color: transparent !important;
}

.sidebar button.sidebar__menu-link--level-3.sidebar__menu-link--active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #3498db;
}

/* 子菜单样式 */
.sidebar__submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  max-height: 600px;
}

.sidebar__submenu--level-0 {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 0 0 8px 8px;
}

.sidebar__submenu--level-1 {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 0 0 6px 6px;
}

.sidebar__submenu--level-2 {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0 0 4px 4px;
  position: relative;
}

/* 第三级子菜单（channel级别）水平显示在右侧 */
.sidebar__submenu--level-2 {
  display: flex;
  flex-direction: column;
}

.sidebar__submenu--level-2 .sidebar__menu-item--level-3 {
  position: relative;
}

.sidebar__submenu--level-2 .sidebar__menu-item--level-3 .sidebar__submenu {
  position: absolute;
  left: 100%;
  top: 0;
  min-width: 200px;
  background: rgba(44, 62, 80, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 1001;
}

.sidebar__submenu--collapsed {
  max-height: 0;
}

.sidebar__submenu-item {
  margin-bottom: 2px;
}

/* 禁用状态样式 */
.sidebar__menu-item--disabled {
  opacity: 0.7;
}

.sidebar__menu-link--disabled {
  cursor: not-allowed !important;
  opacity: 0.7 !important;
  color: rgba(255, 255, 255, 0.6) !important;
}

.sidebar__menu-link--disabled:hover {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.6) !important;
}

.sidebar__submenu-link {
  display: flex;
  align-items: center;
  padding: 8px 20px 8px 40px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  width: 100%;
  text-align: left;
  border-radius: 0;
  position: relative;
  font-size: 13px;
}

.sidebar__submenu-link:hover:not(.sidebar__submenu-link--active) {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.sidebar__submenu-link--active {
  background: rgba(52, 152, 219, 0.3) !important;
  color: #3498db !important;
}

.sidebar__submenu-link--active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #3498db;
}

.sidebar__submenu-text {
  font-size: 13px;
  font-weight: 400;
  white-space: nowrap;
}

.sidebar__footer {
  padding: 20px 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar__user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar__user-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  min-width: 40px;
}

.sidebar__user-info {
  flex: 1;
  min-width: 0;
}

.sidebar__user-name {
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar__user-role {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
  }
}

/* 滚动条样式 */
.sidebar__nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar__nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar__nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar__nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.sidebar__menu-link:focus,
.sidebar__menu-link:focus-visible {
  outline: none;
}



